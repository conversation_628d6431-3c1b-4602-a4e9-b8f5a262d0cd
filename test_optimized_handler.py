#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的DataHandler - 验证去掉向后兼容代码后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.handler import DataHandler, DataConfig, LabelThresholds
from pyqlab.data.dataset.loader import AHFDataLoader

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    # 测试配置类
    config = DataConfig(
        years=["2025"],
        win=10,
        step=1,
        verbose=True
    )
    
    print(f"配置创建成功: win={config.win}, step={config.step}")
    
    # 测试标签阈值
    thresholds = LabelThresholds(
        long_threshold=0.3,
        short_threshold=-0.3
    )
    
    print(f"标签阈值创建成功: long={thresholds.long_threshold}, short={thresholds.short_threshold}")
    
    print("✓ 基本功能测试通过")
    return True

def test_handler_creation():
    """测试DataHandler创建"""
    print("\n测试DataHandler创建...")
    
    try:
        # 创建一个简单的数据加载器配置
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0), (1,1)}
        }
        
        # 创建DataHandler
        handler = DataHandler(
            data_loader=loader_config,
            win=10,
            step=1,
            verbose=False,
            main_fd_name='fd_1_0'
        )
        
        print(f"DataHandler创建成功")
        print(f"配置: win={handler.config.win}, step={handler.config.step}")
        print(f"主要数据名称: {handler.config.main_fd_name}")
        
        # 验证组件是否正确初始化
        assert handler.feature_processor is not None, "FeatureProcessor未初始化"
        assert handler.label_generator is not None, "LabelGenerator未初始化"
        assert handler.normalizer is not None, "DataNormalizer未初始化"
        assert handler.time_feature_generator is not None, "TimeFeatureGenerator未初始化"
        
        print("✓ DataHandler创建测试通过")
        
    except Exception as e:
        print(f"✗ DataHandler创建测试失败: {e}")
        return False
    
    return True

def test_removed_compatibility():
    """测试向后兼容代码是否已移除"""
    print("\n测试向后兼容代码移除...")
    
    try:
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False
        )
        
        # 检查是否还有向后兼容的属性
        compatibility_attrs = [
            'win', 'step', 'filter_win', 'is_filter_extreme', 
            'is_normal', 'verbose', 'timeenc', 'start_date', 
            'end_date', 'years', 'main_fd_name', 'data_path',
            'sel_fd_names', 'x_data', 'y_data', 'encoded_data',
            'direct', 'ins_nums', 'fetch_orig', 'fd_means', 'fd_stds'
        ]
        
        removed_attrs = []
        for attr in compatibility_attrs:
            if not hasattr(handler, attr):
                removed_attrs.append(attr)
        
        print(f"已移除的兼容性属性: {removed_attrs}")
        
        # 检查是否还有向后兼容的方法
        compatibility_methods = [
            'standardize', '_factor_select', '_factor_select_name', 
            '_get_factor_cols', '_setup_compatibility_attributes'
        ]
        
        removed_methods = []
        for method in compatibility_methods:
            if not hasattr(handler, method):
                removed_methods.append(method)
        
        print(f"已移除的兼容性方法: {removed_methods}")
        
        print("✓ 向后兼容代码移除测试通过")
        
    except Exception as e:
        print(f"✗ 向后兼容代码移除测试失败: {e}")
        return False
    
    return True

def test_config_updates():
    """测试配置更新功能"""
    print("\n测试配置更新功能...")
    
    try:
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            verbose=False
        )
        
        # 测试配置更新
        original_win = handler.config.win
        handler.config.win = 15
        
        assert handler.config.win == 15, "配置更新失败"
        print(f"配置更新成功: win从{original_win}更新为{handler.config.win}")
        
        print("✓ 配置更新测试通过")
        
    except Exception as e:
        print(f"✗ 配置更新测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("开始测试优化后的DataHandler")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_handler_creation,
        test_removed_compatibility,
        test_config_updates
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            result = test()
            if result:
                passed += 1
            else:
                print(f"测试 {test.__name__} 失败")
        except Exception as e:
            print(f"测试 {test.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有测试通过！优化成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
