#!/usr/bin/env python3
"""
测试日志输出是否正常工作
"""

def test_logger_configuration():
    """测试日志配置"""
    print("测试日志配置...")
    
    try:
        # 导入模块时会自动配置日志
        from pyqlab.data.dataset.handler import logger, DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 测试logger是否有处理器
        assert len(logger.handlers) > 0, "logger应该有处理器"
        assert logger.level <= 20, f"logger级别应该是INFO或更低，当前: {logger.level}"  # INFO = 20
        
        print("✓ 日志配置正确")
        
        # 测试日志输出
        print("\n测试日志输出...")
        logger.info("这是一条测试INFO日志")
        logger.warning("这是一条测试WARNING日志")
        logger.error("这是一条测试ERROR日志")
        
        print("✓ 日志输出测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_datahandler_logging():
    """测试DataHandler中的日志输出"""
    print("\n测试DataHandler中的日志输出...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        print("\n--- 测试直接传递AHFDataLoader实例的日志 ---")
        loader = AHFDataLoader(
            data_path="f:/featdata/tmp",
            years=["2025"]
        )
        
        # 这应该输出日志信息
        handler = DataHandler(
            data_loader=loader,
            verbose=True  # 启用详细输出
        )
        
        print("\n--- 测试传递配置字典的日志 ---")
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"]
        }
        
        # 这也应该输出日志信息
        handler2 = DataHandler(
            data_loader=loader_config,
            verbose=True
        )
        
        print("✓ DataHandler日志输出测试完成")
        return True
        
    except Exception as e:
        print(f"❌ DataHandler日志测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_verbose_mode():
    """测试详细模式下的日志输出"""
    print("\n测试详细模式下的日志输出...")
    
    try:
        from pyqlab.data.dataset.handler import DataHandler, logger
        from pyqlab.data.dataset.loader import AHFDataLoader
        
        # 创建一个简单的DataHandler
        loader = AHFDataLoader()
        handler = DataHandler(
            data_loader=loader,
            verbose=True,  # 启用详细模式
            sel_fd_names=["RSI", "MACD"]
        )
        
        # 测试一些会产生日志的操作
        print("\n--- 测试因子配置相关的日志 ---")
        handler._recover_factor_cols()
        
        print("\n--- 测试配置更新相关的日志 ---")
        # 这应该产生debug日志（如果日志级别允许的话）
        handler._update_config_safely({
            "win": 20,
            "step": 2,
            "unknown_param": "should_be_ignored"  # 这应该产生warning日志
        })
        
        print("✓ 详细模式日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 详细模式日志测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_log_levels():
    """测试不同日志级别"""
    print("\n测试不同日志级别...")
    
    try:
        from pyqlab.data.dataset.handler import logger
        import logging
        
        # 保存原始级别
        original_level = logger.level
        
        print("\n--- 测试INFO级别 ---")
        logger.setLevel(logging.INFO)
        logger.debug("这条DEBUG日志不应该显示")
        logger.info("这条INFO日志应该显示")
        logger.warning("这条WARNING日志应该显示")
        
        print("\n--- 测试DEBUG级别 ---")
        logger.setLevel(logging.DEBUG)
        logger.debug("这条DEBUG日志现在应该显示")
        logger.info("这条INFO日志应该显示")
        
        print("\n--- 测试WARNING级别 ---")
        logger.setLevel(logging.WARNING)
        logger.info("这条INFO日志不应该显示")
        logger.warning("这条WARNING日志应该显示")
        logger.error("这条ERROR日志应该显示")
        
        # 恢复原始级别
        logger.setLevel(original_level)
        
        print("✓ 不同日志级别测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志级别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始测试日志输出功能...")
    print("=" * 60)
    
    success = True
    success &= test_logger_configuration()
    success &= test_datahandler_logging()
    success &= test_verbose_mode()
    success &= test_different_log_levels()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有日志测试通过！日志输出功能正常！")
        print("\n✅ 验证结果:")
        print("  - 日志配置正确，有控制台处理器")
        print("  - 日志格式化器工作正常")
        print("  - DataHandler中的日志输出正常")
        print("  - 详细模式下的日志输出正常")
        print("  - 不同日志级别工作正常")
        print("  - 日志消息包含时间戳、模块名和级别信息")
    else:
        print("❌ 部分日志测试失败")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
