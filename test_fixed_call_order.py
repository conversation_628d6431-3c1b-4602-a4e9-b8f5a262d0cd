#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的调用顺序 - 验证数据加载后再调用归一化统计数据加载
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.handler import DataHandler
from pyqlab.data.dataset.loader import AHFDataLoader

def test_call_order_fix():
    """测试修正后的调用顺序"""
    print("=" * 60)
    print("测试修正后的调用顺序")
    print("=" * 60)
    
    try:
        # 创建数据加载器配置
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0), (1,1)}
        }
        
        print("\n1. 测试数据加载器的get_fd_names()方法...")
        print("-" * 40)
        
        # 直接创建数据加载器测试
        loader = AHFDataLoader(**loader_config)
        
        print("数据加载前:")
        try:
            fd_names_before = loader.get_fd_names()
            print(f"  get_fd_names() 返回: {fd_names_before}")
        except Exception as e:
            print(f"  get_fd_names() 失败: {e}")
        
        # 加载数据
        print("\n加载数据...")
        data = loader.load()
        print(f"  加载的数据键: {list(data.keys())}")
        
        print("\n数据加载后:")
        try:
            fd_names_after = loader.get_fd_names()
            print(f"  get_fd_names() 返回: {fd_names_after}")
        except Exception as e:
            print(f"  get_fd_names() 失败: {e}")
        
        print("\n2. 测试修正后的setup_data()调用顺序...")
        print("-" * 40)
        
        # 创建DataHandler
        handler = DataHandler(
            data_loader=loader_config,
            win=5,
            step=1,
            is_normal=True,
            verbose=True,
            main_fd_name='fd_1_0'
        )
        
        print(f"初始状态:")
        print(f"  归一化启用: {handler.config.is_normal}")
        print(f"  统计数据已加载: {bool(handler.normalizer.fd_means)}")
        
        # 调用setup_data，现在应该按正确顺序执行
        print(f"\n调用setup_data()...")
        print(f"预期顺序:")
        print(f"  1. 准备因子配置")
        print(f"  2. 加载和预处理原始数据")
        print(f"  3. 自动加载归一化统计数据（在数据加载后）")
        print(f"  4. 验证主要数据存在")
        print(f"  5. 处理主要数据框")
        print(f"  6. 归一化数据")
        print(f"  7. 生成时间特征")
        print(f"  8. 分离标签和特征数据")
        print(f"  9. 设置特征名称")
        print(f"  10. 输出调试信息")
        
        try:
            handler.setup_data()
            print(f"\n✓ setup_data() 调用成功")
            print(f"最终状态:")
            print(f"  归一化启用: {handler.config.is_normal}")
            print(f"  统计数据已加载: {bool(handler.normalizer.fd_means)}")
            print(f"  标签数据形状: {handler.lb_df.shape}")
            print(f"  特征数据形状: {handler.ft_df.shape}")
            
        except Exception as e:
            print(f"✗ setup_data() 调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n3. 测试数据获取功能...")
        print("-" * 40)
        
        try:
            # 测试fetch方法
            result = handler.fetch("long", 5, 0)
            print(f"✓ fetch() 调用成功")
            print(f"  返回数据形状: {[arr.shape for arr in result]}")
            
        except Exception as e:
            print(f"✗ fetch() 调用失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("✓ 所有测试通过！调用顺序修正成功！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_normalization_timing():
    """测试归一化时机的详细情况"""
    print("\n" + "=" * 60)
    print("测试归一化时机详情")
    print("=" * 60)
    
    try:
        loader_config = {
            "data_path": "f:/featdata/tmp",
            "years": ["2025"],
            "fd_set": {(1,0)}
        }
        
        print("\n场景：验证归一化统计数据在正确时机加载")
        print("-" * 40)
        
        handler = DataHandler(
            data_loader=loader_config,
            win=3,
            step=1,
            is_normal=True,
            verbose=False  # 减少日志输出
        )
        
        print(f"步骤1 - 初始状态:")
        print(f"  data_loader类型: {type(handler.data_loader).__name__}")
        print(f"  归一化启用: {handler.config.is_normal}")
        print(f"  统计数据已加载: {bool(handler.normalizer.fd_means)}")
        
        # 手动验证data_loader的状态
        print(f"\n步骤2 - 验证data_loader状态:")
        try:
            # 在数据加载前调用get_fd_names
            fd_names_before = handler.data_loader.get_fd_names()
            print(f"  数据加载前get_fd_names(): {fd_names_before}")
        except Exception as e:
            print(f"  数据加载前get_fd_names()失败: {e}")
        
        # 模拟数据加载
        print(f"\n步骤3 - 模拟数据加载:")
        try:
            data = handler.data_loader.load()
            print(f"  数据加载成功，键: {list(data.keys())}")
            
            # 数据加载后调用get_fd_names
            fd_names_after = handler.data_loader.get_fd_names()
            print(f"  数据加载后get_fd_names(): {fd_names_after}")
            
        except Exception as e:
            print(f"  数据加载失败: {e}")
            return False
        
        print(f"\n步骤4 - 现在调用setup_data()应该成功:")
        try:
            handler.setup_data()
            print(f"  ✓ setup_data()成功")
            print(f"  最终归一化状态: {handler.config.is_normal}")
            print(f"  最终统计数据状态: {bool(handler.normalizer.fd_means)}")
            
        except Exception as e:
            print(f"  ✗ setup_data()失败: {e}")
            return False
        
        print("\n✓ 归一化时机测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 归一化时机测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修正后的调用顺序...")
    
    tests = [
        test_call_order_fix,
        test_normalization_timing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n最终结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！调用顺序修正成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
