#!/usr/bin/env python3
"""
简单的日志测试
"""

import logging

# 测试基本日志功能
print("=== 基本日志测试 ===")

# 创建logger
test_logger = logging.getLogger("test_logger")

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
console_handler.setFormatter(formatter)

# 添加处理器
test_logger.addHandler(console_handler)
test_logger.setLevel(logging.INFO)

# 测试日志输出
print("测试日志输出:")
test_logger.info("这是一条INFO日志")
test_logger.warning("这是一条WARNING日志")
test_logger.error("这是一条ERROR日志")

print("\n=== 测试DataHandler模块的日志 ===")

try:
    # 导入DataHandler模块
    from pyqlab.data.dataset.handler import logger as handler_logger
    
    print(f"Handler logger名称: {handler_logger.name}")
    print(f"Handler logger级别: {handler_logger.level}")
    print(f"Handler logger处理器数量: {len(handler_logger.handlers)}")
    
    # 测试handler logger
    print("测试handler logger输出:")
    handler_logger.info("Handler模块INFO日志")
    handler_logger.warning("Handler模块WARNING日志")
    
    print("✓ DataHandler模块日志正常")
    
except Exception as e:
    print(f"❌ DataHandler模块日志测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 日志测试完成 ===")
